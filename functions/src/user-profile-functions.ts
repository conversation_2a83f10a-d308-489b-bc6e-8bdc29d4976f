import * as admin from "firebase-admin";
import { on<PERSON>all, HttpsError } from "firebase-functions/v2/https";
import { UserEntity } from "./types";
import { prepareUserDataForSave, extractRawTonAddress } from "./utils";

export const changeUserData = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const {
    name,
    tg_id,
    ton_wallet_address,
    raw_ton_wallet_address,
    referral_id,
  } = request.data;
  const userId = request.auth.uid;

  try {
    const db = admin.firestore();

    // Get current user data
    const userDoc = await db.collection("users").doc(userId).get();
    if (!userDoc.exists) {
      throw new HttpsError("not-found", "User not found.");
    }

    const currentUserData = userDoc.data() as UserEntity;

    // Prepare update data
    const updateData: Partial<UserEntity> = {};

    // Update name if provided
    if (name !== undefined) {
      updateData.displayName = name;
    }

    // Update Telegram ID if provided
    if (tg_id !== undefined) {
      updateData.tg_id = tg_id;
    }

    // Update TON wallet address if provided
    if (ton_wallet_address !== undefined) {
      updateData.ton_wallet_address = ton_wallet_address;

      // If no raw_ton_wallet_address is provided, create it from ton_wallet_address
      if (raw_ton_wallet_address === undefined && ton_wallet_address) {
        const rawAddress = extractRawTonAddress(ton_wallet_address);
        if (rawAddress) {
          updateData.raw_ton_wallet_address = rawAddress;
        }
      }
    }

    // Update raw TON wallet address if provided
    if (raw_ton_wallet_address !== undefined) {
      updateData.raw_ton_wallet_address = raw_ton_wallet_address;
    }

    // Update referral ID if provided and user doesn't already have one
    if (referral_id && !currentUserData.referral_id) {
      updateData.referral_id = referral_id;
      console.log(`Setting referral_id for user ${userId}: ${referral_id}`);
    } else if (referral_id && currentUserData.referral_id) {
      console.log(
        `User ${userId} already has referral_id: ${currentUserData.referral_id}, not updating`
      );
    }

    // Prepare data for save (handles TON address processing)
    const preparedData = prepareUserDataForSave(updateData);

    // Update user document
    await db.collection("users").doc(userId).update(preparedData);

    console.log(`User profile updated for ${userId}:`, preparedData);

    return {
      success: true,
      message: "User profile updated successfully",
      updatedFields: Object.keys(preparedData),
    };
  } catch (error) {
    console.error("Error in changeUserData function:", error);

    // Re-throw HttpsError as-is
    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while updating user profile."
    );
  }
});

export const getUserProfile = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const { userId } = request.data;
  const requestingUserId = request.auth.uid;

  // If no userId provided, return current user's profile
  const targetUserId = userId ?? requestingUserId;

  try {
    const db = admin.firestore();

    // Get user data
    const userDoc = await db.collection("users").doc(targetUserId).get();
    if (!userDoc.exists) {
      throw new HttpsError("not-found", "User not found.");
    }

    const userData = userDoc.data() as UserEntity;

    // Return user profile (excluding sensitive data if not the requesting user)
    if (targetUserId !== requestingUserId) {
      // Return limited profile for other users
      return {
        id: userDoc.id,
        displayName: userData.displayName,
        photoURL: userData.photoURL,
        role: userData.role,
      };
    }

    // Return full profile for the requesting user
    return {
      ...userData,
      id: userDoc.id,
    };
  } catch (error) {
    console.error("Error in getUserProfile function:", error);

    // Re-throw HttpsError as-is
    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting user profile."
    );
  }
});
