import { onC<PERSON>, HttpsError } from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import { SendMode, TonClient, WalletContractV5R1, internal } from "@ton/ton";
import { mnemonicToPrivateKey } from "@ton/crypto";
import { UserEntity } from "./types";
import { hasAvailableBalance, spendLockedFunds } from "./balance-service";
import { getMarketplaceWalletMnemonic, isDevelopment } from "./config";
import { MARKETPLACE_REVENUE_USER_ID } from "./constants";
import { getHttpEndpoint } from "@orbs-network/ton-access";

interface WithdrawRevenueData {
  withdrawAmount: number;
  johnDowWallet: string;
}

export const withdrawRevenue = onCall(async (request) => {
  // Validate authentication
  if (!request.auth) {
    throw new HttpsError(
      "unauthenticated",
      "User must be authenticated to withdraw revenue."
    );
  }

  const { withdrawAmount, johnDowWallet } = request.data;

  // Validate input
  if (!withdrawAmount || withdrawAmount <= 0) {
    throw new HttpsError(
      "invalid-argument",
      "Withdrawal amount must be greater than 0."
    );
  }

  if (!johnDowWallet) {
    throw new HttpsError(
      "invalid-argument",
      "John Dow wallet address is required."
    );
  }

  try {
    const db = admin.firestore();
    const userId = request.auth.uid;

    // Check if user is admin
    const userDoc = await db.collection("users").doc(userId).get();
    if (!userDoc.exists) {
      throw new HttpsError("not-found", "User not found.");
    }

    const user = { id: userDoc.id, ...userDoc.data() } as UserEntity;
    if (user.role !== "admin") {
      throw new HttpsError(
        "permission-denied",
        "Only admin users can withdraw revenue."
      );
    }

    // Get marketplace revenue user
    const revenueDoc = await db
      .collection("users")
      .doc(MARKETPLACE_REVENUE_USER_ID)
      .get();

    if (!revenueDoc.exists) {
      throw new HttpsError(
        "not-found",
        "Marketplace revenue account not found."
      );
    }

    const revenueUser = {
      id: revenueDoc.id,
      ...revenueDoc.data(),
    } as UserEntity;

    const availableRevenue = revenueUser.balance
      ? revenueUser.balance.sum - revenueUser.balance.locked
      : 0;

    // Validate minimum balance requirement (must keep 10 TON)
    if (availableRevenue < 10) {
      throw new HttpsError(
        "failed-precondition",
        "Cannot withdraw when revenue balance is less than 10 TON."
      );
    }

    if (withdrawAmount > availableRevenue - 10) {
      throw new HttpsError(
        "failed-precondition",
        `Cannot withdraw more than ${(availableRevenue - 10).toFixed(
          4
        )} TON. Must keep 10 TON minimum.`
      );
    }

    // Check if marketplace revenue has sufficient balance
    const hasBalance = await hasAvailableBalance(
      MARKETPLACE_REVENUE_USER_ID,
      withdrawAmount
    );
    if (!hasBalance) {
      throw new HttpsError(
        "failed-precondition",
        "Insufficient revenue balance for withdrawal."
      );
    }

    // Send full amount to John Dow wallet

    // Deduct the full amount from marketplace revenue balance
    await spendLockedFunds(MARKETPLACE_REVENUE_USER_ID, withdrawAmount);

    const network = isDevelopment() ? "testnet" : "mainnet";
    const endpoint = await getHttpEndpoint({ network });

    // Initialize TON client
    const client = new TonClient({
      endpoint,
    });

    // Get marketplace wallet
    const marketplaceWalletMnemonic = getMarketplaceWalletMnemonic();
    const keyPair = await mnemonicToPrivateKey(
      marketplaceWalletMnemonic.split(" ")
    );

    // Create marketplace wallet contract
    const workchain = 0;
    const marketplaceWallet = WalletContractV5R1.create({
      workchain,
      publicKey: keyPair.publicKey,
    });

    const marketplaceContract = client.open(marketplaceWallet);

    // Get sequence number for the transaction
    const seqno = await marketplaceWallet.getSeqno(
      client.provider(marketplaceWallet.address)
    );

    // Create transfer transaction to John Dow wallet
    const transfer = marketplaceContract.createTransfer({
      seqno,
      sendMode: SendMode.PAY_GAS_SEPARATELY,
      secretKey: keyPair.secretKey,
      messages: [
        internal({
          value: withdrawAmount.toString(),
          to: johnDowWallet,
          body: "Revenue transfer - John Dow",
        }),
      ],
    });

    // Send the transaction
    await marketplaceContract.send(transfer);

    console.log(
      `Revenue transferred: ${withdrawAmount.toFixed(
        4
      )} TON sent to John Dow wallet`
    );

    return {
      success: true,
      message: `Successfully transferred ${withdrawAmount.toFixed(
        4
      )} TON revenue to John Dow`,
      totalAmount: withdrawAmount,
    };
  } catch (error) {
    console.error("Error in withdrawRevenue function:", error);
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError(
      "internal",
      "An error occurred while processing the revenue withdrawal."
    );
  }
});
